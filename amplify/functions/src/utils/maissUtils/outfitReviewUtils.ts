import { MessagePlatform } from "../../config/enums";
import { AIService } from "../../ai/integrations/AIIntegration";
import { StyleProfileService } from "../../services/styleProfileService";

export class OutfitReviewUtils {
  private static aiService = new AIService();
  private static styleProfileService = new StyleProfileService();

  /**
   * Reviews user outfit using AI image analysis
   */
  static async reviewOutfit(
    caseData: any,
    userId: string,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    imageUrl: string
  ) {
    console.log(
      `[OutfitReviewUtils][reviewOutfit] tid=${messageId} Reviewing outfit:`,
      { caseId, userId, messagePlatform, imageUrl }
    );

    try {
      // Get user's style profile for personalized review
      const styleProfile = await this.styleProfileService.getProfile(userId);

      // Create outfit review prompt
      let reviewPrompt = `You are <PERSON><PERSON>, an expert AI Fashion Stylist. Please analyze this outfit image and provide a comprehensive review.

Please evaluate the outfit on the following criteria:
1. Color harmony and coordination
2. Fit and proportions
3. Style coherence
4. Occasion appropriateness
5. Overall aesthetic appeal

Provide:
- An overall rating (1-10)
- Specific feedback on what works well
- Constructive suggestions for improvement
- Style analysis breakdown

Be encouraging and constructive in your feedback.`;

      if (styleProfile) {
        reviewPrompt += `

USER PROFILE CONTEXT:
Gender: ${styleProfile.userGender}
Body Type: ${styleProfile.userBodyType}
Age: ${styleProfile.userAge}
Undertone: ${styleProfile.userUndertone}
Height: ${styleProfile.userHeight}

Please consider the user's profile when providing personalized feedback and suggestions.`;
      }

      // Analyze the outfit image using AI
      const aiResponse = await this.aiService.getGeminiImageAnalysis(
        imageUrl,
        "Outfit review analysis",
        reviewPrompt
      );

      console.log(
        `[OutfitReviewUtils][reviewOutfit] tid=${messageId} Outfit reviewed successfully`
      );

      return {
        statusCode: 200,
        body: JSON.stringify({
          type: "OUTFIT_REVIEWED",
          review: {
            analysis: aiResponse,
            imageUrl,
            reviewedAt: new Date().toISOString(),
            hasStyleProfile: !!styleProfile,
          },
        }),
      };
    } catch (error) {
      console.error(
        `[OutfitReviewUtils][reviewOutfit] tid=${messageId} Error reviewing outfit:`,
        { error, caseId, userId, imageUrl }
      );

      // Fallback response on error
      return {
        statusCode: 200,
        body: JSON.stringify({
          type: "OUTFIT_REVIEWED",
          review: {
            analysis: "I'd love to help review your outfit! Could you please share a clear image of your outfit so I can provide detailed feedback?",
            imageUrl,
            reviewedAt: new Date().toISOString(),
            hasStyleProfile: false,
            error: "Analysis failed, fallback response provided",
          },
        }),
      };
    }
  }
}
