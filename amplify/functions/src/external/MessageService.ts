import { MessagePlatform } from "../config/enums";

export interface WhatsAppMessage {
  id?: string;
  type: "text" | "image" | "interactive";
  text?: {
    body: string;
  };
  image?: {
    link: string;
    caption?: string;
  };
  interactive?: {
    type: "button" | "list";
    body?: {
      text: string;
    };
    action?: {
      buttons?: Array<{
        type: "reply";
        reply: {
          id: string;
          title: string;
        };
      }>;
      sections?: Array<{
        title: string;
        rows: Array<{
          id: string;
          title: string;
          description?: string;
        }>;
      }>;
    };
  };
}

export interface MessageResponse {
  messages?: Array<{
    id: string;
  }>;
  contacts?: Array<{
    wa_id: string;
  }>;
  success: boolean;
  error?: string;
}

export class MessageService {
  private whatsappApiUrl: string;
  private accessToken: string;

  constructor() {
    this.whatsappApiUrl = process.env.WHATSAPP_API_URL || "https://graph.facebook.com/v17.0";
    this.accessToken = process.env.WHATSAPP_ACCESS_TOKEN || "";
  }

  async sendTextMessage(
    to: string,
    message: string,
    options?: { messageId?: string; previewUrl?: boolean },
    platform: MessagePlatform = MessagePlatform.WHATSAPP
  ): Promise<MessageResponse> {
    console.log("[MessageService][sendTextMessage] Sending text message:", { to, message, platform });

    try {
      if (platform === MessagePlatform.NATIVE) {
        // For native platform, just return success (mock implementation)
        return {
          messages: [{ id: `native_${Date.now()}` }],
          success: true,
        };
      }

      // WhatsApp implementation would go here
      // For now, return a mock response
      return {
        messages: [{ id: `wa_${Date.now()}` }],
        success: true,
      };
    } catch (error) {
      console.error("[MessageService][sendTextMessage] Error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  async sendImageMessage(
    to: string,
    imageUrl: string,
    caption?: string,
    options?: { messageId?: string },
    platform: MessagePlatform = MessagePlatform.WHATSAPP
  ): Promise<MessageResponse> {
    console.log("[MessageService][sendImageMessage] Sending image message:", { to, imageUrl, caption, platform });

    try {
      if (platform === MessagePlatform.NATIVE) {
        // For native platform, just return success (mock implementation)
        return {
          messages: [{ id: `native_img_${Date.now()}` }],
          success: true,
        };
      }

      // WhatsApp implementation would go here
      // For now, return a mock response
      return {
        messages: [{ id: `wa_img_${Date.now()}` }],
        success: true,
      };
    } catch (error) {
      console.error("[MessageService][sendImageMessage] Error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  async sendInteractiveMessage(
    to: string,
    message: WhatsAppMessage,
    options?: { messageId?: string },
    platform: MessagePlatform = MessagePlatform.WHATSAPP
  ): Promise<MessageResponse> {
    console.log("[MessageService][sendInteractiveMessage] Sending interactive message:", { to, message, platform });

    try {
      if (platform === MessagePlatform.NATIVE) {
        // For native platform, just return success (mock implementation)
        return {
          messages: [{ id: `native_int_${Date.now()}` }],
          success: true,
        };
      }

      // WhatsApp implementation would go here
      // For now, return a mock response
      return {
        messages: [{ id: `wa_int_${Date.now()}` }],
        success: true,
      };
    } catch (error) {
      console.error("[MessageService][sendInteractiveMessage] Error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }
}
